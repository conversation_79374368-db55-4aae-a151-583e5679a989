import { auth } from './firebase';

const API_BASE_URL = 'http://localhost:5001';

// Make getAuthToken available for export
export async function getAuthToken(): Promise<string> {
  return new Promise((resolve, reject) => {
    const unsubscribe = auth.onAuthStateChanged(async (user) => {
      unsubscribe(); // Unsubscribe immediately after first response
      if (user) {
        try {
          const token = await user.getIdToken();
          resolve(token);
        } catch (error) {
          reject(new Error('Failed to get auth token'));
        }
      } else {
        reject(new Error('No user logged in'));
      }
    });

    // Set a timeout to avoid hanging
    setTimeout(() => {
      unsubscribe();
      reject(new Error('Auth state check timed out'));
    }, 10000); // 10 second timeout
  });
}

// Helper function for authenticated fetches
export async function fetchWithAuth(url: string, options: RequestInit = {}): Promise<Response> {
  const token = await getAuthToken();
  const timestamp = Date.now();
  const nonce = crypto.randomUUID();
  
  try {
    const response = await fetch(url, {
      ...options,
      credentials: 'include',
      headers: {
        ...options.headers,
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        // Only add security headers for non-OPTIONS requests
        ...(options.method !== 'OPTIONS' && {
          'X-Content-Type-Options': 'nosniff',
          'X-Frame-Options': 'SAMEORIGIN',
          'X-Request-Timestamp': timestamp.toString(),
          'X-Request-Nonce': nonce,
        })
      },
    });

    // Verify response timestamp
    const responseTimestamp = response.headers.get('X-Response-Timestamp');
    if (responseTimestamp) {
      const timeDiff = Math.abs(Date.now() - parseInt(responseTimestamp));
      if (timeDiff > 30000) { // 30 seconds max difference
        throw new Error('Response timestamp validation failed');
      }
    }

    // Security header validation for critical endpoints
    const hstsHeader = response.headers.get('Strict-Transport-Security');
    const xFrameHeader = response.headers.get('X-Frame-Options');
    const xContentTypeHeader = response.headers.get('X-Content-Type-Options');
    const cspHeader = response.headers.get('Content-Security-Policy');

    const criticalEndpoints = [
      '/auth',
      '/api/tasks',
      '/api/goals',
      '/api/users',
      '/api/chat'
    ];

    const isCriticalEndpoint = criticalEndpoints.some(endpoint => 
      url.includes(endpoint)
    );

    if (isCriticalEndpoint) {
      const missingHeaders = {
        HSTS: !hstsHeader,
        XFrame: !xFrameHeader,
        XContentType: !xContentTypeHeader,
        CSP: !cspHeader
      };

      const hasMissingHeaders = Object.values(missingHeaders).some(missing => missing);

      if (hasMissingHeaders) {
        console.error('Security headers missing for critical endpoint:', {
          url,
          endpoint: criticalEndpoints.find(endpoint => url.includes(endpoint)),
          missingHeaders,
          allHeaders: [...response.headers.entries()]
        });
        throw new Error('Response missing required security headers');
      }
    }

    return response;
  } catch (error) {
    console.error('fetchWithAuth error:', error);
    throw error;
  }
}

// Authentication helper for backend
export async function authenticateWithBackend(): Promise<{ uid: string; expires_in: number }> {
  const token = await getAuthToken();
  
  const response = await fetch(`${API_BASE_URL}/auth`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ 
      Authorization: token 
    }),
  });

  if (!response.ok) {
    throw new Error('Backend authentication failed');
  }

  return response.json();
}

// Create user document in backend if it doesn't exist
export async function refreshUser(
  firstName?: string,
  lastName?: string,
  handle?: string
): Promise<void> {
  const user = auth.currentUser;
  if (!user) {
    throw new Error('No user logged in');
  }

  try {
    // Try to get user from backend
    const response = await fetchWithAuth(`${API_BASE_URL}/api/users/${user.uid}`);

    if (!response.ok && response.status === 404) {
      // User doesn't exist, create them
      if (!handle || !firstName || !lastName) {
        throw new Error('Handle, first name, and last name are required for new user.');
      }
      await fetchWithAuth(`${API_BASE_URL}/api/users`, {
        method: 'POST',
        body: JSON.stringify({
          handle,
          first_name: firstName,
          last_name: lastName,
        }),
      });
    }
  } catch (error) {
    console.error('Error refreshing user:', error);
    throw error;
  }
}
