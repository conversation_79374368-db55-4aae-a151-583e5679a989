import { useCallback, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  StyleSheet,
  Text,
  TextInput,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { handleAPI } from '../services/api';
import { refreshUser } from '../services/auth';
import { signIn, signUp } from '../services/firebase';

interface SignInScreenProps {
  onSignInSuccess: () => void;
}

export default function SignInScreen({ onSignInSuccess }: SignInScreenProps) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [handle, setHandle] = useState('');
  const [isLogin, setIsLogin] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // State for handle availability check
  const [isHandleChecking, setIsHandleChecking] = useState(false);
  const [isHandleAvailable, setIsHandleAvailable] = useState<boolean | null>(null);
  const [handleError, setHandleError] = useState('');

  // Debounced handle check logic
  const checkHandleAvailability = useCallback(async (currentHandle: string) => {
    if (currentHandle.length < 3) {
      setIsHandleAvailable(null);
      setHandleError('Handle must be at least 3 characters.');
      return;
    }
    setIsHandleChecking(true);
    setHandleError('');
    try {
      const { is_taken } = await handleAPI.checkAvailability(currentHandle);
      setIsHandleAvailable(!is_taken);
      if (is_taken) {
        setHandleError('This handle is already taken.');
      }
    } catch (err) {
      setIsHandleAvailable(null);
      setHandleError('Could not check handle. Please try again.');
    } finally {
      setIsHandleChecking(false);
    }
  }, []);

  useEffect(() => {
    const handler = setTimeout(() => {
      if (handle.trim()) {
        checkHandleAvailability(handle.trim());
      } else {
        setIsHandleAvailable(null);
        setHandleError('');
      }
    }, 500); // 500ms debounce delay

    return () => {
      clearTimeout(handler);
    };
  }, [handle, checkHandleAvailability]);

  const handleSubmit = async () => {
    if (!email.trim() || !password.trim()) {
      setError('Please fill in all fields');
      return;
    }

    if (!isLogin && (!firstName.trim() || !lastName.trim() || !handle.trim())) {
      setError('Please fill in all fields');
      return;
    }

    if (!isLogin && !isHandleAvailable) {
      setError('Please choose an available handle.');
      return;
    }

    setLoading(true);
    setError('');

    try {
      if (isLogin) {
        await signIn(email, password);
      } else {
        await signUp(email, password);
        // Refresh user data after sign up, now with name and handle
        await refreshUser(firstName, lastName, handle);
      }
      onSignInSuccess();
    } catch (error: any) {
      console.error('Auth error:', error);
      setError(error.message || 'Authentication failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <View style={styles.formContainer}>
          <Text style={styles.title}>{isLogin ? 'Sign In' : 'Create Account'}</Text>
          
          {error ? <Text style={styles.errorText}>{error}</Text> : null}

          {!isLogin && (
            <>
              <TextInput
                style={styles.input}
                placeholder="First Name"
                value={firstName}
                onChangeText={setFirstName}
                autoCapitalize="words"
                editable={!loading}
              />
              <TextInput
                style={styles.input}
                placeholder="Last Name"
                value={lastName}
                onChangeText={setLastName}
                autoCapitalize="words"
                editable={!loading}
              />
              <TextInput
                style={styles.input}
                placeholder="Handle (@username)"
                value={handle}
                onChangeText={setHandle}
                autoCapitalize="none"
                editable={!loading}
              />
              {isHandleChecking && <ActivityIndicator size="small" />}
              {!isHandleChecking && handleError && <Text style={styles.errorText}>{handleError}</Text>}
              {!isHandleChecking && isHandleAvailable === true && handle.length >= 3 && (
                <Text style={styles.successText}>Handle is available!</Text>
              )}
            </>
          )}
          
          <TextInput
            style={styles.input}
            placeholder="Email"
            value={email}
            onChangeText={setEmail}
            autoCapitalize="none"
            keyboardType="email-address"
            autoCorrect={false}
            editable={!loading}
          />
          
          <TextInput
            style={styles.input}
            placeholder="Password"
            value={password}
            onChangeText={setPassword}
            secureTextEntry
            editable={!loading}
          />
          
          <Pressable
            style={[styles.button, loading && styles.buttonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.buttonText}>
                {isLogin ? 'Sign In' : 'Sign Up'}
              </Text>
            )}
          </Pressable>
          
          <Pressable
            style={styles.toggleButton}
            onPress={() => setIsLogin(!isLogin)}
            disabled={loading}
          >
            <Text style={styles.toggleText}>
              {isLogin ? 'Need an account? Sign up' : 'Already have an account? Sign in'}
            </Text>
          </Pressable>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  keyboardAvoidingView: {
    flex: 1,
    justifyContent: 'center',
  },
  formContainer: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
    textAlign: 'center',
  },
  input: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    fontSize: 16,
  },
  button: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 8,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  toggleButton: {
    marginTop: 16,
    padding: 12,
    alignItems: 'center',
  },
  toggleText: {
    color: '#007AFF',
    fontSize: 14,
  },
  errorText: {
    color: '#FF3B30',
    marginBottom: 16,
    textAlign: 'center',
  },
  successText: {
    color: '#34C759', // Green color for success
    marginBottom: 16,
    textAlign: 'center',
  },
});
